<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Preview</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            padding-bottom: 10px;
            margin-bottom: 20px;
            color: #555;
        }
        .subject {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .actions {
            margin-top: 20px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 4px;
            text-decoration: none;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
            border: none;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
            border: none;
        }
        .email-content {
            background-color: #fff;
            min-height: 300px;
            overflow: auto;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 4px;
            /* Email-specific styles to match actual email rendering */
            font-family: Arial, Helvetica, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
        }
        .email-content img {
            max-width: 100%;
            height: auto;
            display: block;
        }
        .email-content table {
            border-collapse: collapse;
            width: 100%;
        }
        .email-content td, .email-content th {
            padding: 8px;
            text-align: left;
        }
        /* Fix spacing issues */
        .email-content p {
            margin: 0 0 16px 0;
            padding: 0;
        }
        .email-content br {
            line-height: 1.6;
        }
        /* Remove excessive spacing */
        .email-content > *:first-child {
            margin-top: 0 !important;
        }
        .email-content > *:last-child {
            margin-bottom: 0 !important;
        }
        .preview-note {
            background-color: #fffbea;
            padding: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #ffc107;
            font-size: 14px;
        }
        .email-frame {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
        }
        .email-frame-header {
            background-color: #fff;
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
            margin: -15px -15px 15px -15px;
            border-radius: 6px 6px 0 0;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="subject">Subject: <?php echo $email_subject; ?></div>
            <div>To: <?php echo $contact->email; ?></div>
        </div>
        
        <div class="preview-note">
            <strong>Preview Mode:</strong> This is how your email will appear to recipients. Email clients may render some elements differently.
        </div>

        <div class="email-frame">
            <div class="email-frame-header">
                <strong>Email Client Simulation</strong> - This preview attempts to match how your email will appear in most email clients
            </div>
            <div class="email-content">
                <?php
                // Clean up the email content to remove excessive spacing and format properly
                $cleaned_content = $email_content;

                // Remove excessive line breaks and spaces
                $cleaned_content = preg_replace('/\s*\n\s*\n\s*/', "\n\n", $cleaned_content);
                $cleaned_content = preg_replace('/[ \t]+/', ' ', $cleaned_content);

                // Fix common spacing issues around HTML tags
                $cleaned_content = preg_replace('/>\s+</', '><', $cleaned_content);

                // Ensure proper paragraph spacing
                $cleaned_content = preg_replace('/<\/p>\s*<p[^>]*>/', '</p><p>', $cleaned_content);

                // Remove empty paragraphs
                $cleaned_content = preg_replace('/<p[^>]*>\s*<\/p>/', '', $cleaned_content);

                // Fix spacing around images
                $cleaned_content = preg_replace('/(<img[^>]*>)\s*(<br\s*\/?>)*\s*/', '$1<br>', $cleaned_content);

                echo $cleaned_content;
                ?>
            </div>
        </div>
        
        <div class="actions">
            <form action="<?php echo site_url('vdetails/send_with_template'); ?>" method="post">
                <input type="hidden" name="rep_code" value="<?php echo $rep_code; ?>">
                <input type="hidden" name="visitor_type" value="<?php echo $visitor_type; ?>">
                <input type="hidden" name="template_id" value="<?php echo $template_id; ?>">
                
                <button type="submit" class="btn btn-primary">Send Email</button>
                <a href="javascript:window.close();" class="btn btn-secondary">Close</a>
            </form>
        </div>
    </div>
</body>
</html>

