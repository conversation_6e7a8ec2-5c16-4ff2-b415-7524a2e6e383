<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
use Ngekoding\CodeIgniterDataTables\DataTables;
require_once FCPATH.'vendor/autoload.php';
class Vdetails extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
			ini_set('memory_limit','256M');
			$this->load->library('session');
			$this->load->helper('form');
			$this->load->helper('url');
			$this->load->helper('html');
			$this->load->helper('array');  // used by element() in function updateSurvey
			$this->load->database();
			//$this->load->library('form_validation');
			//$this->load->library('pagination');
			$this->load->library('encryption');
			//load the login model
			// $this->load->model('vrs_model');
			// $this->load->model('vrs_read_model');
			$this->load->model('master_model');
			$this->load->model('vdetails_model');
			$this->load->library('phpmailer_lib');
			$this->dashboard = $this->load->model('dashboard_model');

	}

	public function index(){
		$set_data = $this->session->all_userdata(); //E5
     	$data = sessionRights($set_data);
     	$decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $this->input->get('d'));
       	$get = $this->encryption->decrypt($decryptX);
       	$data['tmp'] = explode('#',$get);
       	$data['ViewHeader'] = $data['tmp'][3];
       	$data['asofDate'] = date('F d, Y h:i A');
       	// diagnostics($data['tmp']);
		$this->load->view('vrs_viewdetails',$data);
	}

//====================================================

	public function ajax_datatable(){
		$decryptX = str_replace(array('-', '_', '^'), array('+', '/', '='), $this->input->get('d'));
       	$get = $this->encryption->decrypt($decryptX);
		list($visitorType, $queryFilter, $visitorSector, $visitorView) = explode('#', $get);

       	$data['fcode'] = $_SESSION["sessionData"]['fcode'];
       	$qryAdd = $this->_generate_qryAdd($get);
		$query = $this->vdetails_model->joinTable("v_contact_profile as A","v_attendance as B",
			"A.rep_code,A.barcode,A.co_name,A.email,A.email2,A.work_email,A.remarks,A.mobile,A.cont_per_fn,A.cont_per_ln,A.country,A.continent,A.region,A.pid,A.add_st,A.add_city,A.zipcode,A.email2,A.webpage,B.pre_reg,B.buyerclass,B.date_apply,B.visitor_type,B.validated,B.emailed,B.date_validated,B.sector as name,A.remarks,B.assigned_table",
			"A.rep_code = B.rep_code",$qryAdd." A.deleted= '0' and B.fair_code = '".$data['fcode']."'","");

		$datatables = new DataTables($query, '3');
		$datatables->addColumnAlias('A.rep_code', 'rep_code');
		$datatables->addColumnAlias('B.date_apply', 'date_apply');
		$datatables->addColumnAlias('B.pre_reg', 'pre_reg');
		if($visitorType=="TRADE BUYER"){
			$datatables->addColumn('action', function($row){
				return '<div class="btn-group">
							<button type="button" class="btn btn-success btn-sm resend-email-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="TB" title="Resend Email Confirmation">
								<i class="fa fa-envelope"></i>
							</button>
							<button type="button" class="btn btn-primary btn-sm template-select-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="TB" title="Select Template">
								<i class="fa fa-cog"></i>
							</button>
							<button type="button" class="btn btn-info btn-sm preview-email-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="TB" title="Preview Email">
								<i class="fa fa-eye"></i>
							</button>
						</div>';
			});
		}
		elseif($visitorType=="GUEST"){
			$datatables->addColumn('action', function($row){
				return '<div class="btn-group">
							<button type="button" class="btn btn-success btn-sm resend-email-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="GUEST" title="Resend Email Confirmation">
								<i class="fa fa-envelope"></i>
							</button>
							<button type="button" class="btn btn-primary btn-sm template-select-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="GUEST" title="Select Template">
								<i class="fa fa-cog"></i>
							</button>
							<button type="button" class="btn btn-info btn-sm preview-email-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="GUEST" title="Preview Email">
								<i class="fa fa-eye"></i>
							</button>
						</div>';
			});
		}
		elseif($visitorType=="GENERAL PUBLIC"){
			$datatables->addColumn('action', function($row){
				return '<div class="btn-group">
							<button type="button" class="btn btn-success btn-sm resend-email-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="GP" title="Resend Email Confirmation">
								<i class="fa fa-envelope"></i>
							</button>
							<button type="button" class="btn btn-primary btn-sm template-select-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="GP" title="Select Template">
								<i class="fa fa-cog"></i>
							</button>
							<button type="button" class="btn btn-info btn-sm preview-email-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="GP" title="Preview Email">
								<i class="fa fa-eye"></i>
							</button>
						</div>';
			});
		}
		elseif($visitorType=="MEDIA"){
			$datatables->addColumn('action', function($row){
				return '<div class="btn-group">
							<button type="button" class="btn btn-success btn-sm resend-email-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="MEDIA" title="Resend Email Confirmation">
								<i class="fa fa-envelope"></i>
							</button>
							<button type="button" class="btn btn-primary btn-sm template-select-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="MEDIA" title="Select Template">
								<i class="fa fa-cog"></i>
							</button>
							<button type="button" class="btn btn-info btn-sm preview-email-btn" data-rep-code="'.$row->rep_code.'" data-visitor-type="MEDIA" title="Preview Email">
								<i class="fa fa-eye"></i>
							</button>
						</div>';
			});
		}
		else{
			$datatables->addColumn('action', function($row){
				return '';
			});
		}
		$datatables->addSequenceNumber('row_number')
			->asObject()
			->generate();
	}

//====================================================

	private function _generate_qryAdd($get){
		list($visitorType, $queryFilter, $visitorSector, $dashboardView) = explode('#',$get);
		if(strtolower($dashboardView)=='cumulative'){
			if(strtolower($visitorType)=='trade buyer'){
				return "B.visitor_type LIKE '%TRADE BUYER%' and B.reg_status='T' and ";
			}
			if(strtolower($visitorType)=='guest'){
				return "B.visitor_type LIKE '%GUEST%' and B.reg_status='T' and ";
			}
			if(strtolower($visitorType)=='general public'){
				return "B.visitor_type LIKE '%GENERAL PUBLIC%' and B.reg_status='T' and ";
			}
			if(strtolower($visitorType)=='media'){
				return "B.visitor_type LIKE '%MEDIA%' and B.reg_status='T' and ";
			}

		}
		if(strtolower($dashboardView)=='pre-registered'){
			if(strtolower($visitorType)=='trade buyer'){
				return "B.visitor_type LIKE '%TRADE BUYER%' and B.pre_reg='P' and ";
			}
			if(strtolower($visitorType)=='guest'){
				return "B.visitor_type LIKE '%GUEST%' and B.pre_reg='P' and ";
			}
			if(strtolower($visitorType)=='general public'){
				return "B.visitor_type LIKE '%GENERAL PUBLIC%' and B.pre_reg='P' and ";
			}
			if(strtolower($visitorType)=='media'){
				return "B.visitor_type LIKE '%MEDIA%' and B.pre_reg='P' and ";
			}
		}

		//cummulative
		//media = B.visitor_type LIKE '%MEDIA%' and B.reg_status='T' and
		//paying visitor = B.visitor_type LIKE '%GENERAL PUBLIC%' and B.reg_status='T' and
		//non-trade = B.visitor_type LIKE '%GUEST%' and B.reg_status='T' and
		// TB = B.visitor_type LIKE '%TRADE BUYER%' and B.reg_status='T' and
		//
		// pre reg
		//media = B.visitor_type LIKE '%MEDIA%' and B.pre_reg='P' and
		//paying visitor = B.visitor_type LIKE '%GENERAL PUBLIC%' and B.pre_reg='P' and
		//non-trade = B.visitor_type LIKE '%GUEST%' and B.pre_reg='P' and
		// TB = B.visitor_type LIKE '%TRADE BUYER%' and B.pre_reg='P' and
		//
	}

	//====================================================

	public function resend_email($rep_code,$visitor_type)
	{

		$set_data = $this->session->all_userdata();
		$data = sessionRights($set_data);
		$data['fcode'] = $_SESSION["sessionData"]['fcode'];

		// Get contact and project information
		$contact = $this->dashboard_model->getContact($rep_code, $data['fcode']);

		if (!$contact){
			// Contact not found
			$this->session->set_flashdata('error', 'Contact not found.');
			redirect($_SERVER['HTTP_REFERER']);
			return;
		}	

		switch ($visitor_type) {
			case "TB":
				$messageCode = 'CMESS1';
				break;
			case "GUEST":
				$messageCode = 'CMESS2';
				break;
			case "GP":
				$messageCode = 'CMESS3';
				break;
			case "MEDIA":
				$messageCode = 'CMESS4';
				break;
			default:
				$messageCode = '';
				break;
		}

		// Get email buyer confirmation template from v_reference
		$emailTemplate = $this->dashboard_model->getEmailTemplate($contact->sector, $data['fcode'], $messageCode);

		if (!$emailTemplate) {
			//if ajax return right json
			if ($this->input->is_ajax_request()) {
				echo json_encode(array('success' => false, 'message' => 'Email template not found.'));
				return;
			}
			// Template not found
			$this->session->set_flashdata('error', 'Email template not found.');
			redirect($_SERVER['HTTP_REFERER']);
			return;
		}
		$project = $this->dashboard_model->getProject($data['fcode']);

		// Generate QR code
		$qrcodeFilename = $this->createQR($contact->visitor_type,$contact->position, $contact->cont_per_fn, $contact->cont_per_ln, 
		$contact->email, $contact->mobile, $contact->co_name, $contact->country, $rep_code, $data['fcode'], "no", "P", $this->sanitizeInput($contact->visitor_status, '', true));
	
		// Create QR code HTML
		$qrValue = base_url("idbadge/".$qrcodeFilename);
		$qrcode = "<img src='".$qrValue."' alt='Your QRcode' width='150' height='150'>";

		// Prepare email content
		$html = $emailTemplate->header.$emailTemplate->content_message.$emailTemplate->footer;
		$Subject = $emailTemplate->content_subject;

		// Replace placeholders in the email content
		$html = str_replace("{name}", $contact->cont_per_fn . ' ' . $contact->cont_per_ln, $html);
		$html = str_replace("{company}", $contact->co_name, $html);
		$html = str_replace("{qrcode}", $qrcode, $html);
		$html = str_replace("{repcode}", $rep_code, $html);
		$html = str_replace("{promocode}", $contact->item_code, $html);


		// Send email using PHPMailer
		$mail = $this->phpmailer_lib->load();

		// SMTP configuration
		$mail->isSMTP();
		$mail->Host = SMTP_HOST;
		$mail->SMTPAuth = SMTP_AUTH;
		$mail->SMTPAutoTLS = SMTP_AutoTLS;
		$mail->Username = SMTP_USER;
		$mail->Password = SMTP_PASS;
		$mail->SMTPSecure = SMTP_ENCRYPT;
		$mail->Port = SMTP_PORT;

		// Set proper headers for email
		$mail->XMailer = 'PHP/' . phpversion();
		$mail->CharSet = 'UTF-8';
		$mail->Encoding = 'base64';
		$mail->isHTML(true);
		
		// Add custom header to prevent being marked as spam
		$mail->addCustomHeader('X-Mailer', 'PHP/' . phpversion());
		$mail->addCustomHeader('X-Priority', '3');
		
		// Sender and recipient
		$mail->setFrom($project->emailto_registration, $project->description);
		$mail->addReplyTo($project->emailto_registration, $project->description);
		$mail->addAddress($contact->email, $contact->cont_per_fn . ' ' . $contact->cont_per_ln);

		// $mail->setFrom('<EMAIL>');
		// $mail->addReplyTo('<EMAIL>', 'ifex');
		// $mail->addAddress('<EMAIL>', 'eric tomas');

		// Add CC and BCC
		// if (!empty($project->emailto)) {
		// 	$mail->addBCC($project->emailto);
		// }

		// Email content
		$mail->Subject = $Subject;
		$mail->Body = $html;
		//$mail->AltBody = strip_tags(str_replace('<br>', "\n", $html)); // Plain text alternative

		// Debug mode - comment out for production
		// $mail->SMTPDebug = 2;
		// $mail->Debugoutput = 'html';

		// Send email
		if (!$mail->send()) {
			echo json_encode(array('success' => false, 'message' => 'Mailer Error: ' . $mail->ErrorInfo));
		} else {
			// Update email sent status in database
			// $this->vdetails_model->updateEmailSent($rep_code, $data['fcode']);
			echo json_encode(array('success' => true, 'message' => 'Email confirmation has been sent successfully'));
		}
		exit();
	}

	//====================================================

	/**
	 * Create QR code for visitor
	 */
	private function createQR($visitorType,$position,$fn,$ln,$email,$mobile,$comp,$ctry,$rcode,$fcode,$XincludeAllProfile,$vrsPrereg,$vstat)
	{
		$this->load->library('infiQr');
		
		$tempDir = FCPATH."idbadge/";

		// $fn = $this->sanitizeInput($fn, 'N/A', true);
		// $ln = $this->sanitizeInput($ln, 'N/A', true);
		// $email = $this->sanitizeInput($email);
		// $mobile = $this->sanitizeInput($mobile);
		// $position = $this->sanitizeInput($position);
		// $comp = $this->sanitizeInput($comp, 'N/A', true);
		// $ctry = $this->sanitizeInput($ctry);
		// $visitorType = $this->sanitizeInput($visitorType);
		// $vrsPrereg = $this->sanitizeInput($vrsPrereg, 'P', true);
		// $vstat = $this->sanitizeInput($vstat);
		// $rcode = $this->sanitizeInput($rcode, '', true); // Registration code is required
		// $fcode = $this->sanitizeInput($fcode, '', true); // Fair code is required
		
		// Building raw data in vCard format
	    $codeContents  = 'BEGIN:VCARD'."\n";
	    $codeContents .= 'FN:'.$fn."\n";
	    $codeContents .= 'N:'.$ln."\n";
	    $codeContents .= 'EMAIL:'.$email."\n";

	    if($XincludeAllProfile=="yes") {
	    	$codeContents .= 'TEL:'.$mobile."\n";
			$codeContents .= 'TITLE:'.$position."\n";
		}	 

	    $codeContents .= 'ORG:'.$comp."\n";
	    $codeContents .= 'ADR:'.$ctry."\n";    	    
	    $codeContents .= 'VRS:'.$vrsPrereg."\n";
	    $codeContents .= 'STATUS:'.$vstat."\n";

	    if(isset($visitorType) ) {
	    	$codeContents .= 'VTYPE:'.$visitorType."\n";	
	    }
	    $codeContents .= 'RCODE:'.$rcode."\n";
	    $codeContents .= 'END:VCARD';
	    
	    // Generate QR code and store image
		QRcode::png($codeContents, $tempDir.$fcode."-".$rcode.'.png', QR_ECLEVEL_L, 2);

		// Return filename
		return $fcode."-".$rcode.'.png';
   }

   	/**
	 * Create Standard Vcard QR code for visitor
	 * 
	 * @param string $visitorType Type of visitor (TRADE BUYER, GUEST, etc.)
	 * @param string $position Visitor's job position/title
	 * @param string $fn First name
	 * @param string $ln Last name
	 * @param string $email Email address
	 * @param string $mobile Mobile number
	 * @param string $comp Company name
	 * @param string $ctry Country
	 * @param string $rcode Registration code
	 * @param string $fcode Fair/event code
	 * @param string $XincludeAllProfile Whether to include all profile details
	 * @param string $vrsPrereg Registration type (P=pre-registered, onsite, etc.)
	 * @param string $vstat Visitor status
	 * @return string QR code image filename
	 */
	private function createStandardVcardQR($visitorType, $position, $fn, $ln, $email, $mobile, $comp, $ctry, $rcode, $fcode, $XincludeAllProfile, $vrsPrereg, $vstat)
	{
		// Load QR code library
		$this->load->library('infiQr');
		
		// Set directory for storing QR code images
		$tempDir = FCPATH."idbadge/";
		
		// Create timestamp for unique filename
		$vtimestamp = date('YmdHis');
		
		// Build QR code content in vCard format without separators
		// Format: BEGIN:VCARDFN:JoeN:SampleEMAIL:<EMAIL>:CompanyADR:CountryVRS:PSTATUS:VTYPE:TRADE BUYERRCODE:123456END:VCARD
		$codeContents = 'BEGIN:VCARD';
		$codeContents .= 'FN:' . $fn;
		$codeContents .= 'N:' . $ln;
		
		if (!empty($email)) {
			$codeContents .= 'EMAIL:' . $email;
		}
		
		// Include mobile number and position if full profile is requested
		if ($XincludeAllProfile == "yes") {
			if (!empty($mobile)) {
				$codeContents .= 'TEL:' . $mobile;
			}
			if (!empty($position)) {
				$codeContents .= 'TITLE:' . $position;
			}
		}
		
		// Add company, country and registration details
		$codeContents .= 'ORG:' . $comp;
		$codeContents .= 'ADR:' . $ctry;
		$codeContents .= 'VRS:' . $vrsPrereg;
		$codeContents .= 'STATUS:' . $vstat;
		
		// Add visitor type if provided
		if (!empty($visitorType)) {
			$codeContents .= 'VTYPE:' . $visitorType;
		}
		
		// Add registration code and end tag
		$codeContents .= 'RCODE:' . $rcode;
		$codeContents .= 'END:VCARD';
		
		// Generate QR code image
		// Parameters: content, filename, error correction level, size
		QRcode::png($codeContents, $tempDir . $fcode . "-" . $rcode . "-" . $vtimestamp . '.png', QR_ECLEVEL_L, 2);
		
		// Return only the filename (not full path)
		return $fcode . "-" . $rcode . "-" . $vtimestamp . '.png';
	}

	//====================================================

	private function sanitizeInput($value, $default = '', $required = false)
	{
		// Check if value is null, empty string, or only whitespace
		if ($value === null || $value === '' || (is_string($value) && trim($value) === '')) {
			return $required ? $default : '';
		}
		
		// For non-empty values, trim whitespace and return
		return is_string($value) ? trim($value) : $value;
	}

	public function get_email_templates($visitor_type)
	{
		if (!$this->input->is_ajax_request()) {
			show_404();
			return;
		}
		
		$set_data = $this->session->all_userdata();
		$data = sessionRights($set_data);
		$data['fcode'] = $_SESSION["sessionData"]['fcode'];
		
		// Get all available templates for this visitor type
		$templates = $this->dashboard_model->getEmailTemplatesByType($data['fcode'], $visitor_type);
		
		echo json_encode(array('success' => true, 'templates' => $templates));
	}
	
	public function preview_email($rep_code, $visitor_type, $template_id = null)
	{
		$set_data = $this->session->all_userdata();
		$data = sessionRights($set_data);
		$data['fcode'] = $_SESSION["sessionData"]['fcode'];
		
		// Get contact information
		$contact = $this->dashboard_model->getContact($rep_code, $data['fcode']);
		
		if (!$contact) {
			if ($this->input->is_ajax_request()) {
				echo json_encode(array('success' => false, 'message' => 'Contact not found.'));
				return;
			}
			$this->session->set_flashdata('error', 'Contact not found.');
			redirect($_SERVER['HTTP_REFERER']);
			return;
		}
		
		// Get template
		$messageCode = $template_id ? $template_id : $this->_getDefaultTemplateCode($visitor_type);
		$emailTemplate = $this->dashboard_model->getEmailTemplate($contact->sector, $data['fcode'], $messageCode);
		
		if (!$emailTemplate) {
			if ($this->input->is_ajax_request()) {
				echo json_encode(array('success' => false, 'message' => 'Email template not found.'));
				return;
			}
			$this->session->set_flashdata('error', 'Email template not found.');
			redirect($_SERVER['HTTP_REFERER']);
			return;
		}
		
		// Generate QR code
		$qrcodeFilename = $this->createQR($contact->visitor_type, $contact->position, $contact->cont_per_fn, $contact->cont_per_ln, 
		$contact->email, $contact->mobile, $contact->co_name, $contact->country, $rep_code, $data['fcode'], "no", "P", $this->sanitizeInput($contact->visitor_status, '', true));
	
		// Create QR code HTML
		$qrValue = base_url("idbadge/".$qrcodeFilename);
		$qrcode = "<img src='".$qrValue."' alt='Your QRcode' width='150' height='150'>";
		
		// Prepare email content with proper spacing handling
		$header = trim($emailTemplate->header ?? '');
		$content = trim($emailTemplate->content_message ?? '');
		$footer = trim($emailTemplate->footer ?? '');

		// Combine parts with proper spacing
		$html = '';
		if (!empty($header)) {
			$html .= $header;
			if (!empty($content)) $html .= "\n\n";
		}
		if (!empty($content)) {
			$html .= $content;
			if (!empty($footer)) $html .= "\n\n";
		}
		if (!empty($footer)) {
			$html .= $footer;
		}

		$Subject = $emailTemplate->content_subject;

		// Replace placeholders in the email content
		$html = str_replace("{name}", $contact->cont_per_fn . ' ' . $contact->cont_per_ln, $html);
		$html = str_replace("{company}", $contact->co_name, $html);
		$html = str_replace("{qrcode}", $qrcode, $html);
		$html = str_replace("{repcode}", $rep_code, $html);
		$html = str_replace("{promocode}", $contact->item_code, $html);

		// Clean up the HTML content for better email rendering
		$html = $this->cleanEmailContent($html);
		
		if ($this->input->is_ajax_request()) {
			echo json_encode(array(
				'success' => true, 
				'subject' => $Subject,
				'html' => $html,
				'template_id' => $messageCode
			));
			return;
		}
		
		// If not AJAX, show preview in a view
		$data['email_subject'] = $Subject;
		$data['email_content'] = $html;
		$data['rep_code'] = $rep_code;
		$data['visitor_type'] = $visitor_type;
		$data['template_id'] = $messageCode;
		
		$this->load->view('vrs_email_preview', $data);
	}

	/**
	 * Clean email content to remove excessive spacing and format properly for email clients
	 * @param string $content The raw email content
	 * @return string Cleaned email content
	 */
	private function cleanEmailContent($content)
	{
		// Remove excessive whitespace and normalize line breaks
		$content = preg_replace('/\r\n|\r/', "\n", $content);

		// Remove excessive spaces and tabs
		$content = preg_replace('/[ \t]+/', ' ', $content);

		// Remove excessive line breaks (more than 2 consecutive)
		$content = preg_replace('/\n{3,}/', "\n\n", $content);

		// Clean up spacing around HTML tags
		$content = preg_replace('/>\s+</', '><', $content);

		// Fix spacing around block elements
		$content = preg_replace('/<\/(div|p|h[1-6]|table|tr)>\s*<(div|p|h[1-6]|table|tr)/', '</$1><$2', $content);

		// Remove empty paragraphs and divs
		$content = preg_replace('/<(p|div)[^>]*>\s*<\/(p|div)>/', '', $content);

		// Ensure proper spacing around images
		$content = preg_replace('/(<img[^>]*>)\s*(<br\s*\/?>)*\s*/', '$1<br>', $content);

		// Fix multiple consecutive <br> tags
		$content = preg_replace('/(<br\s*\/?>){3,}/', '<br><br>', $content);

		// Trim whitespace from beginning and end
		$content = trim($content);

		return $content;
	}
	
	public function send_with_template()
	{
		if (!$this->input->is_ajax_request()) {
			show_404();
			return;
		}
		
		$rep_code = $this->input->post('rep_code');
		$visitor_type = $this->input->post('visitor_type');
		$template_id = $this->input->post('template_id');
		
		if (!$rep_code || !$visitor_type || !$template_id) {
			echo json_encode(array('success' => false, 'message' => 'Missing required parameters.'));
			return;
		}
		
		$set_data = $this->session->all_userdata();
		$data = sessionRights($set_data);
		$data['fcode'] = $_SESSION["sessionData"]['fcode'];
		
		// Get contact information
		$contact = $this->dashboard_model->getContact($rep_code, $data['fcode']);
		
		if (!$contact) {
			echo json_encode(array('success' => false, 'message' => 'Contact not found.'));
			return;
		}
		
		// Get template
		$emailTemplate = $this->dashboard_model->getEmailTemplate($contact->sector, $data['fcode'], $template_id);
		
		if (!$emailTemplate) {
			echo json_encode(array('success' => false, 'message' => 'Email template not found.'));
			return;
		}
		
		// Generate QR code
		$qrcodeFilename = $this->createQR($contact->visitor_type, $contact->position, $contact->cont_per_fn, $contact->cont_per_ln, 
		$contact->email, $contact->mobile, $contact->co_name, $contact->country, $rep_code, $data['fcode'], "no", "P", $this->sanitizeInput($contact->visitor_status, '', true));
	
		// Create QR code HTML
		$qrValue = base_url("idbadge/".$qrcodeFilename);
		$qrcode = "<img src='".$qrValue."' alt='Your QRcode' width='150' height='150'>";
		
		// Prepare email content with proper spacing handling
		$header = trim($emailTemplate->header ?? '');
		$content = trim($emailTemplate->content_message ?? '');
		$footer = trim($emailTemplate->footer ?? '');

		// Combine parts with proper spacing
		$html = '';
		if (!empty($header)) {
			$html .= $header;
			if (!empty($content)) $html .= "\n\n";
		}
		if (!empty($content)) {
			$html .= $content;
			if (!empty($footer)) $html .= "\n\n";
		}
		if (!empty($footer)) {
			$html .= $footer;
		}

		$Subject = $emailTemplate->content_subject;

		// Replace placeholders in the email content
		$html = str_replace("{name}", $contact->cont_per_fn . ' ' . $contact->cont_per_ln, $html);
		$html = str_replace("{company}", $contact->co_name, $html);
		$html = str_replace("{qrcode}", $qrcode, $html);
		$html = str_replace("{repcode}", $rep_code, $html);
		$html = str_replace("{promocode}", $contact->item_code, $html);

		// Clean up the HTML content for better email rendering
		$html = $this->cleanEmailContent($html);
		
		// Send email using PHPMailer
		$mail = $this->phpmailer_lib->load();
		
		// SMTP configuration
		$mail->isSMTP();
		$mail->Host = SMTP_HOST;
		$mail->SMTPAuth = SMTP_AUTH;
		$mail->SMTPAutoTLS = SMTP_AutoTLS;
		$mail->Username = SMTP_USER;
		$mail->Password = SMTP_PASS;
		$mail->SMTPSecure = SMTP_ENCRYPT;
		$mail->Port = SMTP_PORT;
		
		// Set proper headers for email
		$mail->XMailer = 'PHP/' . phpversion();
		$mail->CharSet = 'UTF-8';
		
		$mail->setFrom(EMAIL_FROM, EMAIL_FROM_NAME);
		$mail->addReplyTo(EMAIL_REPLY_TO, EMAIL_FROM_NAME);
		
		// Add recipient
		$mail->addAddress($contact->email);
		
		// Email subject
		$mail->Subject = $Subject;
		
		// Set email format to HTML
		$mail->isHTML(true);
		
		// Email body content
		$mail->Body = $html;
		
		// Send email
		if(!$mail->send()){
			echo json_encode(array('success' => false, 'message' => 'Failed to send email: ' . $mail->ErrorInfo));
			return;
		}
		
		// Update emailed status in database
		$this->dashboard_model->updateEmailedStatus($rep_code, $data['fcode']);
		
		echo json_encode(array('success' => true, 'message' => 'Email sent successfully.'));
	}
	
	private function _getDefaultTemplateCode($visitor_type)
	{
		switch ($visitor_type) {
			case "TB":
				return 'CMESS1';
			case "GUEST":
				return 'CMESS2';
			case "GP":
				return 'CMESS3';
			case "MEDIA":
				return 'CMESS4';
			default:
				return '';
		}
	}

}

